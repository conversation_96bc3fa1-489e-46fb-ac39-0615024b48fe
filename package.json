{"name": "kmi-0827", "version": "0.0.1", "private": true, "description": "", "license": "MIT", "scripts": {"analyze": "ANALYZE=1 kmi build", "build:online": "KMI_ENV=online kmi build", "build:prt": "KMI_ENV=prt kmi build", "build:staging": "KMI_ENV=staging kmi build", "dev": "PORT=8070 kmi dev", "postinstall": "kmi setup", "lint": "kmi lint --eslint-only --fix", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx", "prebuild": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "prepare": "husky install", "prettier": "prettier **/* --write", "preview": "kmi preview", "setup": "kmi setup", "swet": "kmi swet checkout", "swet:ls": "kmi swet list", "tsc": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write", "npm run lint-staged:js"]}, "eslintIgnore": ["dist/", "node_modules/", ".kmi", ".kmi-production"], "dependencies": {"@ad/pro-components": "^0.0.7", "@kmi/plugin-swet": "^1.4.2", "@kmi/react": "^1.4.2", "@m-ui/icons": "^2.5.1", "@m-ui/react": "^1.11.9", "react-error-boundary": "^4.0.9", "@yoda/bridge": "^2.0.0"}, "devDependencies": {"@ad/eslint-config-ad": "^2.0.0-rc.12", "@ksuni/kmi-plugin-monitor": "^0.0.18", "@types/mockjs": "^1.0.7", "@types/react": "^18.0.9", "@types/react-dom": "^18.0.5", "eslint": "^8.16.0", "husky": "^7.0.4", "lint-staged": "^12.3.3", "mockjs": "^1.1.0", "prettier": "2.5.1", "prettier-plugin-organize-imports": "2.3.4", "prettier-plugin-packagejson": "2.2.17", "typescript": "^5.0.0"}, "authors": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "shang<PERSON><EMAIL>"}, "pnpm": {"peerDependencyRules": {"ignoreMissing": ["react", "react-dom", "postcss", "webpack", "@babel/core", "vite", "postcss-syntax", "react-refresh", "csstype", "rollup", "styled-components"], "allowedVersions": {"dva-core": "2"}}}, "templateConfig": {"source": "create-kmi"}}