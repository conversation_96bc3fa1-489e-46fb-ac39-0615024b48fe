import mockjs from 'mockjs';

export default {
  'GET /api/report/datasource/listPhysicalTable': mockjs.mock({
    code: 0,
    data: {
      records: [
        {
          cluster: '@string',
          engineType: '@string',
          maxQps: '@integer(1, 100)',
          name: '@string',
          p95Latency: '@integer(1, 100)',
          queryCount: '@integer(1, 100)',
        },
        {
          cluster: '@string',
          engineType: '@string',
          maxQps: '@integer(1, 100)',
          name: '@string',
          p95Latency: '@integer(1, 100)',
          queryCount: '@integer(1, 100)',
        },
        {
          cluster: '@string',
          engineType: '@string',
          maxQps: '@integer(1, 100)',
          name: '@string',
          p95Latency: '@integer(1, 100)',
          queryCount: '@integer(1, 100)',
        },
        {
          cluster: '@string',
          engineType: '@string',
          maxQps: '@integer(1, 100)',
          name: '@string',
          p95Latency: '@integer(1, 100)',
          queryCount: '@integer(1, 100)',
        },
      ],
      total: '@integer(1, 100)',
    },
    draftId: '@integer(1, 100000)',
    message: '@string',
    result: 1,
    errorMsg: '请求失败，请稍后再试...',
  }),
};
