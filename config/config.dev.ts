import { defineConfig } from '@kmi/react';

// 仅开发环境生效配置, 默认生效
export default defineConfig({
  /**
   * 开发环境配置成 publicPath: '/'，方便在 config/config.ts 直接配置生产环境的 publicPath
   */
  publicPath: '/',
  /**
   * @name 代理配置
   * @description 可以让你的本地服务器代理到你的服务器上，这样你就可以访问服务器的数据了
   * @see 要注意以下 代理只能在本地开发时使用，build 之后就无法使用了，这里在登录部分配合KAPI使用，表单页面使用本地mock
   * @doc 代理介绍 https://k-mi.corp.kuaishou.com/guide/essentials/proxy.html
   * @doc 代理配置 https://k-mi.corp.kuaishou.com/config/config.html#proxy
   */
  proxy: {
    // 配置kdev代理
    '/api': {
      target: 'https://koasproxy.corp.kuaishou.com/proxy/98d1c5ac',
      changeOrigin: true,
    }
  },
});
