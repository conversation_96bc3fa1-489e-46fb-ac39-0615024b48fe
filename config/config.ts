// https://k-mi.corp.kuaishou.com/config/config.html
import { defineConfig } from '@kmi/react';
import routes from './routes';

export default defineConfig({
  /**
   * @name 包管理工具选择
   */
  npmClient: 'pnpm',
  /**
   * @name 路由的配置，不在路由中引入的文件不会编译
   * @doc https://k-mi.corp.kuaishou.com/guide/essentials/route.html
   */
  routes,
  /**
   * @name m-ui 插件
   * @description 内置了 babel import 插件
   */
  mui: {
    style: 'less',
  },
  /**
   * @name 网络请求配置
   * @description 它基于 axios 和 ahooks 的 useRequest 提供了一套统一的网络请求和错误处理方案。
   * @doc https://k-mi.corp.kuaishou.com/guide/advanced/request.html
   */
  request: {},
  /**
   * @name 请求文档转译&mock
   * @description 它基于 swet-cli 和 mockjs 提供了一套统一的API维护和本地开发方案。
   * @doc https://k-mi.corp.kuaishou.com/guide/advanced/swet.html
   */
  swet: {
    // 数据源
    sources: [''],
    // 支持TS
    supportTs: true,
    // API 生成位置
    outDir: 'src/services/swet',
  },
  /**
   * @name layout 插件
   * @doc https://k-mi.corp.kuaishou.com/guide/advanced/layout.html
   */
  layout: {
    // layout主题
    theme: 'light',
    // layout布局方式
    layout: 'side',
    // 菜单分离配置
    splitMenus: true,
    // 菜单鼠标放置预览
    menuPreview: true,
    // 左上角Logo
    logo: 'https://js-ad.a.yximgs.com/kos/nlav12243/logo/logo.png',
    // 左上角标题
    title: 'Kmi',
  },
  /**
   * 埋点和监控插件
   * @doc https://k-mi.corp.kuaishou.com/guide/advanced/monitor.html
   */
  monitor: {
    //【必填】radar(雷达)项目 id 在雷达平台新建/编辑项目获取[雷达平台](https://radar-plus.corp.kuaishou.com/projects)
    projectId: '',
    //【必填】当前埋点产品的 product_name 在[Weblogger平台申请](https://data-track.corp.kuaishou.com/#/entity/list)
    productName: '',
    scene: 'hybrid',
  },
});
