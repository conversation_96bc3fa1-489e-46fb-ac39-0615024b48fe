import type { RequestConfig, RunTimeLayoutConfig } from '@kmi/react';
import { message as ms } from '@m-ui/react';

enum ErrorShowType {
  SILENT = 0,
  WARN_MESSAGE = 1,
  ERROR_MESSAGE = 2,
  NOTIFICATION = 3,
  REDIRECT = 9,
}

interface ResponseStructure {
  code: number;
  data: any;
  message?: string;
  showType?: ErrorShowType;
  errorCode?: number;
}

/**
 * @name 运行时request个性化配置
 * @description 可进行自定义错误拦截等相关信息
 * @doc https://k-mi.corp.kuaishou.com/guide/advanced/request.html#%E8%BF%90%E8%A1%8C%E6%97%B6%E9%85%8D%E7%BD%AE%E7%A4%BA%E4%BE%8B
 */
export const request: RequestConfig = {
  errorConfig: {
    errorHandler: (error: any, opts: any) => {
      if (opts?.skipErrorHandler) {
        throw error;
      }
      // 请求拦截报错
      ms.error(error.message || '请求返回有误，已拦截');
    },
    errorThrower: (res: ResponseStructure) => {
      const { code, data, message, errorCode } = res;
      if (code !== 0) {
        const error: any = new Error(message);
        error.name = 'BizError';
        error.info = { errorCode, message, data };
        throw error;
      }
    },
  },
};

/**
 * @name 运行时布局布局配置
 * @description 运行时的相关内容可不在config中进行配置
 * @doc https://k-mi.corp.kuaishou.com/guide/advanced/layout-menu.html#%E8%BF%90%E8%A1%8C%E6%97%B6%E9%85%8D%E7%BD%AE
 */
export const layout: RunTimeLayoutConfig = ({ initialState }: { [prop: string]: any }) => {
  return {
    logout: () => {
      console.log('logout');
    },
    ...initialState?.settings,
  };
};
