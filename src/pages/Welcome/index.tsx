import { Card, Button, Input } from '@m-ui/react';
import { useState } from 'react';
import { getPriorityList, getUnfinishApi } from '@/services/custom';


const Welcome: React.FC = () => {
  const [value, setValue] = useState('');
  return (
    <>
      <Button type='primary' onClick={() => {
        getPriorityList().then(res => console.log(res))
      }}>
        调用server已开发完成的接口(/api/mock/manage/apiManage/main/api/priority/list)
      </Button>
      <hr />
      <div style={{ display: 'flex', flexDirection: 'row', gap: 10 }}
      >
        <Button onClick={() => {
          getUnfinishApi(value).then(res => console.log(res))
        }}>
          调用server未开发完成的接口(/api/mock/manage/apiManage/main/api/dianshang/show)
        </Button>
        name=
        <Input value={value} onChange={e => setValue(e.target.value)} />
      </div>
    </>
  );
};

export default Welcome;
