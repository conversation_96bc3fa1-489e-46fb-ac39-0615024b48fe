// 使用本地mock数据
import { BankTwoTone } from '@m-ui/icons';
import type { ActionType, ProColumns } from '@ad/pro-components';
import { ProTable, TableDropdown } from '@ad/pro-components';
import { Button } from '@m-ui/react';
import { useRef } from 'react';
import { getPriorityList } from '@/services/custom';

/**
 * @name pro-table的列配置
 * @description 列配置可同步到查询相关内容上，一次配置两次使用
 * @doc https://kmi-components.test.gifshow.com/components/layout#route
 */
const columns: ProColumns[] = [
  {
    dataIndex: 'index',
    valueType: 'indexBorder',
    width: 48,
  },
  {
    title: '名称',
    dataIndex: 'name',
    copyable: true,
    ellipsis: true,
    tip: '名称过长会自动收缩',
    formItemProps: {
      rules: [
        {
          required: true,
          message: '此项为必填项',
        },
      ],
    },
  },
  {
    title: '引擎类型',
    dataIndex: 'engineType',
  },
  {
    disable: true,
    title: '最大QPS',
    dataIndex: 'maxQps',
    search: false,
  },
  {
    disable: true,
    title: '参数个数',
    dataIndex: 'queryCount',
    search: false,
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    valueType: 'dateRange',
    hideInTable: true,
    search: {
      transform: (value) => {
        return {
          startTime: value[0],
          endTime: value[1],
        };
      },
    },
  },
  {
    title: '操作',
    valueType: 'option',
    key: 'option',
    render: (_text, record, _, action) => [
      <a
        key="editable"
        onClick={() => {
          action?.startEditable?.(record.id);
        }}
      >
        编辑
      </a>,
      <a href={record.url} target="_blank" rel="noopener noreferrer" key="view">
        查看
      </a>,
      <TableDropdown
        key="actionGroup"
        onSelect={() => action?.reload()}
        menus={[
          { key: 'copy', name: '复制' },
          { key: 'delete', name: '删除' },
        ]}
      />,
    ],
  },
];

const TablePage = () => {
  const actionRef = useRef<ActionType>();
  return (
    <ProTable
      columns={columns}
      actionRef={actionRef}
      cardBordered
      request={async (params, sort, filter) => {
        const { pageSize, keyword, current } = params;
        const res = await getPriorityList({
          pageSize,
          searchKey: keyword,
          pageNum: current,
          ...sort,
          ...filter,
        });
        const { records, total } = res.data;
        return {
          data: records,
          total,
        };
      }}
      editable={{
        type: 'multiple',
      }}
      columnsState={{
        persistenceKey: 'pro-table-singe-demos',
        persistenceType: 'localStorage',
        onChange(value) {
          console.log('value: ', value);
        },
      }}
      rowKey="cluster"
      search={{
        labelWidth: 'auto',
      }}
      form={{
        // 由于配置了 transform，提交的参与与定义的不同这里需要转化一下
        syncToUrl: (values, type) => {
          if (type === 'get') {
            return {
              ...values,
              created_at: [values.startTime, values.endTime],
            };
          }
          return values;
        },
      }}
      pagination={{
        pageSize: 5,
        onChange: (page) => console.log(page),
      }}
      dateFormatter="string"
      toolBarRender={() => [
        <Button key="button" icon={<BankTwoTone />} type="primary">
          新建
        </Button>,
      ]}
    />
  );
};

export default TablePage;
