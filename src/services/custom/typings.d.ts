declare namespace API {
  interface ResultWrapper<T> {
    code: number;
    data: T;
  }

  interface CurrentUser {
    name?: string;
    avatar?: string;
    userid?: string;
    email?: string;
    signature?: string;
    title?: string;
    group?: string;
    tags?: { key?: string; label?: string }[];
    notifyCount?: number;
    unreadCount?: number;
    country?: string;
    access?: string;
    geographic?: {
      province?: { label?: string; key?: string };
      city?: { label?: string; key?: string };
    };
    address?: string;
    phone?: string;
  }

  interface LoginResult {
    status?: string;
    type?: string;
    currentAuthority?: string;
  }

  interface PageParams {
    current?: number;
    pageSize?: number;
  }

  interface FakeCaptcha {
    code?: number;
    status?: string;
  }

  interface LoginParams {
    username?: string;
    password?: string;
    autoLogin?: boolean;
    type?: string;
  }

  interface PagingTableList {
    records: {
      cluster: string;
      engineType: string;
      maxQps: number;
      name: string;
      p95Latency: number;
      queryCount: number;
    }[];
    total: number;
  }
}
